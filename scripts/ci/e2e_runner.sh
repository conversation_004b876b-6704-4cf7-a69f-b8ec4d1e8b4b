#!/bin/bash

# Usage: ./scripts/ci/e2e_runner.sh [--cy|--pw]
# Example: ./scripts/ci/e2e_runner.sh --pw
# Example (for local testing): yarn test:e2e:local --pw OR yarn test:e2e:local --cy

# This script is what we run to initialize E2E tests (<PERSON><PERSON> or <PERSON><PERSON>).

# Cypress runs over VNC in a codespace.
# To run this script against local changes and connect using a native VNC
# app (mac's Screen Sharing for example), first start up the admin dashboard,
# then run the command `yarn test:e2e:local --cy` at the top level of the repo
# and authenticate with password "transcend".
# You can view the tests at http://127.0.0.1:6080/ in your browser.
# You can also connect using a web VNC client if don't have a native VNC program with `yarn test:e2e:local:web --<runner>`.
# It's also possible to run your local tests against dev to sanity check
# test function failures or otherwise benchmark your tests, you can do this with
# `yarn test:e2e:dev --<runner>` OR `yarn test:e2e:dev:web --<runner>`

# Playwright does not run over VNC, but this script is used to set up the etc/hosts file
# so that we have the correct aliases for our test privacy centers. It can also run against
# dev or staging environments.

# --- Argument Parsing ---
RUNNER=""

for arg in "$@"
do
    case $arg in
        --cy)
        RUNNER="cypress"
        shift # Remove --cy from processing
        ;;
        --pw)
        RUNNER="playwright"
        shift # Remove --pw from processing
        ;;
    esac
done

# Prompt user if no runner flag is provided
if [[ -z "$RUNNER" ]]; then
    echo "No runner specified. Please choose one:"
    select choice in "Cypress" "Playwright"; do
        case $choice in
            Cypress ) RUNNER="cypress"; break;;
            Playwright ) RUNNER="playwright"; break;;
            * ) echo "Invalid choice. Please select 1 or 2.";;
        esac
    done
fi

echo "Selected Runner: $RUNNER"

# --- Setup ---
HOSTNAME="$(cat /etc/hostname)"
ETC_HOSTS="/etc/hosts"
echo 'Writing ' "$ETC_HOSTS"
grep -qn "$HOSTNAME" "$ETC_HOSTS" || echo 'Adding hostname to /etc/hosts...' && echo "127.0.0.1 $HOSTNAME" >> "$ETC_HOSTS"

# --- Construct Hostname Block ---
HOSTNAMES="127.0.0.1   yo.com
127.0.0.1   transcend.yo.com
127.0.0.1   patreon.yo.com
127.0.0.1   cypress-intl-messages.yo.com
127.0.0.1   cypress-jwt-login.yo.com
127.0.0.1   cypress-policies.yo.com
127.0.0.1   cypress-privacy-center-access.yo.com
127.0.0.1   cypress-privacy-center-erasure.yo.com
127.0.0.1   cypress-color-palette.yo.com
127.0.0.1   cypress-general-settings.yo.com
127.0.0.1   cypress-bulk-pa-v.yo.com
127.0.0.1   cypress-export-to-csv.yo.com
127.0.0.1   cypress-requests.yo.com"

# Add the constructed hostnames to /etc/hosts
sudo tee -a "$ETC_HOSTS" >> /dev/null <<< "$HOSTNAMES"


# --- Runner-Specific Commands ---
echo "Starting $RUNNER..."
if [[ "$RUNNER" == "cypress" ]]; then
    # Determine config file based on E2E_ENV
    CONFIG_FILE=""
    if [[ $E2E_ENV = 'dev' ]]; then CONFIG_FILE=dev; fi
    if [[ $E2E_ENV = 'staging' ]]; then CONFIG_FILE=staging; fi

    # Start VNC
    netstat -ano | grep -q 5901 || echo 'Starting VNC server/client...' && bash /usr/local/share/desktop-init.sh

    # Open site if specified
    if [[ -n $SITE ]]
    then
      if [[ "$(uname)" = 'Linux' ]]
      then xdg-open "$SITE"
      else open "$SITE"
      fi;
    fi;


    # Add before running Cypress
    export NO_AT_BRIDGE=1
    unset DBUS_SESSION_BUS_ADDRESS

    # Run Cypress command
    if [[ -n $CONFIG_FILE ]]; then
        yarn cypress open --env configFile="$CONFIG_FILE"
    else
        yarn cypress open
    fi
elif [[ "$RUNNER" == "playwright" ]]; then
    # Determine config file based on E2E_ENV
    if [[ -n $E2E_ENV ]]; then
        export PLAYWRIGHT_ENV_FILE="../../envs/${E2E_ENV}.json"
    fi

    # Run Playwright command
    yarn workspace @main/playwright playwright test --ui-port=5902 --ui-host=0.0.0.0
else
    echo "Error: Invalid runner selected."
    exit 1
fi
